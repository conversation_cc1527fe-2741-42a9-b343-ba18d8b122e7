package warranty

import "time"

type WarrantyWithMT struct {
	ID             int       `json:"id"`
	Barcode        string    `json:"barcode"`
	Number         string    `json:"number"`
	Imei           string    `json:"imei"`
	ExtBarcode     string    `json:"ext_barcode"`
	CreatedAt      time.Time `json:"created_at"`
	Status         int8      `json:"status"` // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	Model          string    `json:"model"`
	ModelID        int       `json:"model_id"`
	CustomerPhone  string    `json:"customer_phone"`
	ProductDate    time.Time `json:"product_date"`
	State          int8      `json:"state"`
	ActivatedAtOld time.Time `json:"activated_at_old"`

	CategoryId    int `json:"category_id"`     // machine_type表 分类id
	ExtBarcodeNum int `json:"ext_barcode_num"` // machine_type表 副机条码数量
}

type WarrantyInfoReq struct {
	Barcode         string `form:"barcode"`
	CustomerName    string `form:"customer_name"`
	CustomerPhone   string `form:"customer_phone"`
	BuyDateStart    string `form:"buy_date_start"`
	BuyDateEnd      string `form:"buy_date_end"`
	Model           string `form:"model"`
	Pid             int    `form:"top_agency"`    // top agency
	SecondaryAgency int    `form:"second_agency"` // secondary agency
	EndpointCode    string `form:"endpoint_code"` // 终端编码
	Page            int    `form:"page"`
	PageSize        int    `form:"page_size"`
}

type WarrantyInfo struct {
	ID         string
	Barcode    string
	ExtBarcode string
	Model      string
	Salesman   string
	BuyDate    time.Time
	// 顾客信息
	CustomerName    string
	CustomerPhone   string
	CustomerSex     string
	CustomerAddress string
	// 学生信息
	StudentName     string
	StudentSchool   string
	StudentGrade    string
	StudentBirthday string
	Assessment      int
}

type WarrantyInfoWithEndpoint struct {
	WarrantyInfo
	Name    string // 终端名称
	Address string
	Phone   string
	Manager string
}

type ReturnListReq struct {
	Barcode         string `form:"barcode"`
	ReturnTimeBegin string `form:"return_time_begin"`
	ReturnTimeEnd   string `form:"return_time_end"`
	Page            int    `form:"page"`
	PageSize        int    `form:"page_size"`
}

type ReturnListResp struct {
	ID       int
	Barcode  string
	Model    string
	Reason   string // 退货原因
	Manager  string
	ReturnAt time.Time // 退货时间
	// 终端信息
	Name            string
	Address         string
	Phone           string
	EndpointManager string
	// 客户信息
	CustomerName string
}

type ExchangeListReq struct {
	Barcode           string `form:"barcode"`
	ExchangeTimeBegin string `form:"exchange_time_begin"`
	ExchangeTimeEnd   string `form:"exchange_time_end"`
	Page              int    `form:"page"`
	PageSize          int    `form:"page_size"`
}

type ExchangeListResp struct {
	ID         int
	Barcode    string
	BarcodeNew string
	ModelOld   string
	ModelNew   string
	Manager    string
	Reason     string    // 换货原因
	CreatedAt  time.Time // 换货时间
	// 终端信息
	EndpointName    string
	EndpointAddress string
	EndpointPhone   string
	EndpointManager string
	// 客户信息
	CustomerName string
}

type ImportResp struct {
	Msg        string `json:"import_msg"`
	SuccessMsg string `json:"success_msg"`
	FailedMsg  string `json:"failed_msg"`
}

type WarrantiesExportResp struct {
	Code          string `json:"code"`
	EndpointName  string `json:"endpoint_name"`
	Model         string `json:"model"`
	Barcode       string `json:"barcode"`
	BuyDate       string `json:"buy_date"`
	CustomerPhone string `json:"customer_phone"`
	CustomerName  string `json:"customer_name"`
	StudentName   string `json:"student_name"`
	StudentSchool string `json:"student_school"`
	StudentGrade  string `json:"student_grade"`
}
