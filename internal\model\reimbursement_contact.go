package model

import (
	"time"
)

// ReimbursementContact 报销联系人模型
type ReimbursementContact struct {
	ID        uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID       uint      `gorm:"column:uid;not null;comment:用户id" json:"uid"`
	TopAgency uint      `gorm:"column:top_agency;not null;comment:一级代理id" json:"top_agency"`
	Name      string    `gorm:"column:name;type:varchar(50);not null;comment:姓名" json:"name"`
	Phone     string    `gorm:"column:phone;type:varchar(20);not null;comment:手机号" json:"phone"`
	Province  int       `gorm:"column:province;not null;comment:省份id" json:"province"`
	City      int       `gorm:"column:city;not null;comment:城市id" json:"city"`
	District  int       `gorm:"column:district;not null;comment:地区id" json:"district"`
	Address   string    `gorm:"column:address;type:varchar(255);not null;comment:详细地址" json:"address"`
	Default   int       `gorm:"column:default;type:tinyint(1);default:0;comment:是否默认联系人" json:"default"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (ReimbursementContact) TableName() string {
	return "reimbursement_contact"
}
