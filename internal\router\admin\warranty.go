package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/service/contact"
	"marketing/internal/service/prototype"

	repo "marketing/internal/dao"
	repo1 "marketing/internal/dao/admin_user"
	repo2 "marketing/internal/dao/endpoint"
	repo3 "marketing/internal/dao/prototype"
	"marketing/internal/handler/warranty"
	"marketing/internal/pkg/db"
	"marketing/internal/service"
)

type WarrantyRouter struct {
	warrantyHandler       warranty.WarrantyHandler
	warrantyReturnHandler warranty.WarrantyReturnHandler
}

func NewWarrantyRouter() *WarrantyRouter {
	var Db = db.GetDB()
	warrantyRepo := repo.NewWarrantyDao(Db)
	userRepo := repo1.NewUserDao(Db)
	endpointRepo := repo2.NewEndpointDao(Db)
	prototypeRepo := repo3.NewPrototypeDao(Db)
	prototypeCache := repo3.NewPrototypeCache()
	PrototypeConfigDao := repo3.NewPrototypeConfig(Db)
	machineTypeRepo := repo.NewMachineTypeDao()
	commonRepo := repo.NewCommonGorm(Db)
	contactRepo := repo.NewContact(Db)
	contactSvc := contact.NewContact(contactRepo)
	prototypeSvc := prototype.NewPrototypeService(prototypeRepo, endpointRepo, PrototypeConfigDao, prototypeCache)
	warrantySvc := service.NewWarrantyService(warrantyRepo,
		userRepo,
		endpointRepo,
		prototypeRepo,
		machineTypeRepo,
		commonRepo,
		prototypeCache,
		contactSvc,
		prototypeSvc)
	warrantyHandler := warranty.NewWarrantyHandler(warrantySvc)
	return &WarrantyRouter{
		warrantyHandler: warrantyHandler,
	}
}

func (wr *WarrantyRouter) Register(r *gin.RouterGroup) {

	w := r.Group("/warranty")
	{
		w.GET("/endpoints", wr.warrantyHandler.Endpoints) // 搜索终端
		// 保单管理
		w.POST("/create", wr.warrantyHandler.Create)                   // 创建保单
		w.GET("/list", wr.warrantyHandler.List)                        // 查询保单
		w.GET("/:id", wr.warrantyHandler.GetOneWarranty)               // 查询单条保单信息
		w.PUT("/:id/:assessment", wr.warrantyHandler.UpdateAssessment) // 更新保单评估状态
		w.PUT("/:id/edit", wr.warrantyHandler.Update)                  // 编辑保单
		w.POST("/group_import", wr.warrantyHandler.GroupImport)        // 团购导入
		w.GET("/export", wr.warrantyHandler.Export)                    // 导出保卡数据
		// 退货
		w.POST("/return", wr.warrantyHandler.Return)             // 退货保单管理
		w.PUT("/return/:id/edit", wr.warrantyHandler.ReturnEdit) // 编辑退货记录
		w.GET("/return_list", wr.warrantyHandler.ReturnList)     // 退货记录
		// 换货
		w.POST("/exchange", wr.warrantyHandler.Exchange)             // 换货申请
		w.GET("/exchange_list", wr.warrantyHandler.ExchangeList)     // 换货列表
		w.GET("/exchange/:id", wr.warrantyHandler.ExchangeGet)       // 获取单条换货记录
		w.PUT("/exchange/:id/edit", wr.warrantyHandler.ExchangeEdit) // 编辑换货记录
		// SN查询
		w.GET("/sn/machine", wr.warrantyHandler.SNList) // SN查询
	}
}
