package reimbursement

import (
	"errors"
	"fmt"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/gin-gonic/gin"

	api "marketing/internal/api/reimbursement"

	"gorm.io/gorm"
)

type ReimbursementRepository interface {
	GetPromotionalProductsQuickPickStat(c *gin.Context, req *api.PromotionalProductsQuickPickStatReq) (*api.PromotionalProductsQuickPickStatResp, error)
	GetAdvertExpenseQuickPickStat(c *gin.Context, req *api.AdvertExpenseQuickPickStatReq) (*api.AdvertExpenseQuickPickStatResp, error)
	GetPromotionalProductsDetail(c *gin.Context, req *api.OrderReq) (*api.PromotionalProductsDetailResp, error)
	GetReimbursementProducts(c *gin.Context, orderID int) ([]api.PromotionalProductDetail, error)
	GetReimbursementApplyInfo(c *gin.Context, orderID int, applyOrderType string) ([]api.ApplicationInfo, error)
	GetAdvertExpenseList(c *gin.Context, req *api.AdvertExpenseListReq) (*api.AdvertExpenseListResp, error)
	GetAdvertExpenseDetail(c *gin.Context, req *api.OrderReq) (*api.AdvertExpenseDetailResp, error)
	GetAdvertExpenseOrder(c *gin.Context, orderID int, agency map[string]int) (*model.ReimbursementAdvertExpenseList, error)
	GetPromotionalProductsOrder(c *gin.Context, orderID int, agency map[string]int) (*model.ReimbursementPromotionalProductsList, error)
	MaterialSignIn(c *gin.Context, orderID int, orderType string) (bool, error)
	GetPolicyByOrder(c *gin.Context, orderID int, policyType int) (*model.ReimbursementPolicy, error)
	InvalidAdvertExpense(c *gin.Context, orderID int) (bool, error)
	InvalidPromotionalProducts(c *gin.Context, orderID int) (bool, error)
	AuditAdvertExpense(c *gin.Context, req *api.AuditAdvertExpenseReq, UserID uint) (bool, error)
	GetSummaryShortcutStat(c *gin.Context, req *api.SummaryShortcutStatReq, policyType string) (*api.ShortcutStatResp, error)
	GetSummaryList(c *gin.Context, req *api.SummaryShortcutStatReq) ([]*api.SummaryListItem, int64, error)
	ReimbursementApplyOrder(c *gin.Context, orderID int) (*model.ReimbursementApplyOrderSummary, error)
	AdvertExpenseOrderSplit(c *gin.Context, req *api.AdvertExpenseOrderSplitReq, order *model.ReimbursementApplyOrderSummary) error
	ApplyOrderSummaryInvalid(c *gin.Context, orderID int) error
	GetOrdersDetail(c *gin.Context, companyID int, ordersID []int) ([]model.ReimbursementApplyOrderSummary, error)
	ReimbursementCreate(c *gin.Context, req *api.ApplyOrderSummarySubmitReq, policy *model.ReimbursementPolicy, orders []*OrderItem, balance *model.ReimbursementBalanceStandard) error
	GetOrderDetail(c *gin.Context, orderID int) (*model.ReimbursementList, error)
	GetReimbursementPolicy(c *gin.Context, policyID int) (*model.ReimbursementPolicy, error)
	ReimbursementRetrial(c *gin.Context, uid uint, reimbursementID int, summaryIDs []int, order *model.ReimbursementList, policy *model.ReimbursementPolicy, balance *model.ReimbursementBalanceStandard) error
	ReimbursementAudit(c *gin.Context, req *api.ReimbursementAuditReq, order *model.ReimbursementList, policyInfo *model.ReimbursementPolicy, balance *model.ReimbursementBalanceStandard) error
	ReviewVoucherPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) error
	ReviewDataPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq, order *model.ReimbursementPromotionalProductsList) error
	ReimbursementSummaryGroupByPolicy(c *gin.Context, policies []model.ReimbursementPolicy) ([]*api.ReimbursementSummaryGroupByPolicyResp, error)
	AddExpressInfo(c *gin.Context, req *api.AddExpressInfoReq) error
	GetReimbursementAllSummaryTotal(c *gin.Context, req *api.CompanySummaryReq) (*api.CompanySummaryStatResp, error)
	GetReimbursementAllSummaryPage(c *gin.Context, req *api.CompanySummaryReq) ([]*api.ReimbursementSummaryPageResp, int64, error)
	PromotionalProductsSplit(c *gin.Context, req *api.PromotionalProductsSplitReq) error
	GetPendingPromotionalProducts(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error)
	GetPendingAdvertExpense(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error)
	GetPendingReimbursementList(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error)
	GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, int64, error)
	GetPolicyCompanySummary(c *gin.Context, policyID, companyID int, topAgency uint) (*api.ClientSummaryResp, error)
	GetPromotionalProductsOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	GetAdvertExpenseOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	GetPromotionalProductsApplySummaryOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	GetAdvertExpenseApplySummaryOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	GetReimbursementOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error)
	ReimbursementClientDetail(c *gin.Context, orderID int, topAgency uint) (*api.ReimbursementDetail, error)
	GetDetailSummaryInfo(ctx *gin.Context, orderID int) ([]*api.ReimbursementSummary, error)
	ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error
	ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error)
	ApplyPromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) error
	ChangePromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) (bool, error)
	UploadReceipt(c *gin.Context, req *api.UploadReceiptReq) error
	AmountConfirm(c *gin.Context, req *api.AmountConfirmReq) error
	GetSummaryOrderList(c *gin.Context, orderID int) ([]*model.ReimbursementApplyOrderSummary, error)
	GetPolicyQuantityQuota(c *gin.Context, policyID int, orderID int) (int64, error)
}

type repo struct {
	db *gorm.DB
}

func NewReimbursementRepository(db *gorm.DB) ReimbursementRepository {
	return &repo{db: db}
}

type OrderItem struct {
	ID       int     `json:"id"`
	Amount   float64 `json:"amount"`
	Quantity int64   `json:"quantity"`
	TurnType int     `json:"turn_type"`
}

// GetReimbursementApplyInfo 获取报销图片申请信息
func (r *repo) GetReimbursementApplyInfo(c *gin.Context, orderID int, applyOrderType string) ([]api.ApplicationInfo, error) {
	var applicationInfos []api.ApplicationInfo

	// 这是用于存储查询结果的临时切片
	var results []struct {
		ID      int    `gorm:"column:id"`
		Explain string `gorm:"column:explain"`
		URL     string `gorm:"column:url"`
		Type    string `gorm:"column:type"`
	}

	// 使用 GORM 进行查询
	err := r.db.WithContext(c).Table("reimbursement_apply_info").
		Select("id", "explain", "url", "type").
		Where("apply_order_id = ? AND apply_order_type = ? AND deleted_at IS NULL", orderID, applyOrderType).
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 将结果填充到 applicationInfos 中
	for _, result := range results {
		applicationInfos = append(applicationInfos, api.ApplicationInfo{
			ID:      result.ID,
			Explain: result.Explain,
			URL:     utils.AddPrefix(result.URL),
			Type:    cast.ToInt(result.Type),
		})
	}

	return applicationInfos, nil
}

func (r *repo) ReimbursementSummaryGroupByPolicy(c *gin.Context, policies []model.ReimbursementPolicy) ([]*api.ReimbursementSummaryGroupByPolicyResp, error) {
	var results []*api.ReimbursementSummaryGroupByPolicyResp

	// 创建一个 map 来保存 policy ID 到 policy 的映射，以便后续使用
	policyMap := make(map[uint]model.ReimbursementPolicy)
	var ids []uint
	for _, policy := range policies {
		policyID := cast.ToUint(policy.ID)
		policyMap[policyID] = policy
		ids = append(ids, policyID)
	}

	// 用于保存查询结果的中间结构
	type QueryResult struct {
		PolicyID       uint
		PolicyName     string
		PolicyType     string
		StandardType   string
		Archive        int
		PendingCount   int
		InProcessCount int
		CompletedCount int
		InvalidCount   int
	}

	var queryResults []QueryResult

	// 进行联合查询，把所有需要的数据一次性取回
	err := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Select(`
			rp.id as policy_id,
			rp.name as policy_name,
			rp.policy_type,
			rp.standard_type,
			rp.archive,
			SUM(raos.status=0) as pending_count,
			SUM(raos.status=1) as in_process_count,
			SUM(raos.status=2) as completed_count,
			SUM(raos.status=-100) as invalid_count
		`).
		Joins("RIGHT JOIN reimbursement_policy rp ON raos.policy_id = rp.id").
		Where("(raos.status >= 0 OR raos.status = -100) AND ((rp.id IN ? AND raos.turn_type != 1) OR raos.turn_type = 1)", ids).
		Group("rp.id, rp.name, rp.policy_type, rp.standard_type, rp.archive").
		Scan(&queryResults).Error

	if err != nil {
		return nil, err
	}

	// 创建一个 map 来存储查询结果以便快速检索
	queryResultMap := make(map[uint]QueryResult)
	for _, queryResult := range queryResults {
		queryResultMap[queryResult.PolicyID] = queryResult
	}

	// 按照输入 policies 的顺序生成输出
	for _, policy := range policies {
		policyID := cast.ToUint(policy.ID)
		queryResult, exists := queryResultMap[policyID]

		var summary *api.ReimbursementSummaryGroupByPolicyResp
		if exists {
			summary = &api.ReimbursementSummaryGroupByPolicyResp{
				PolicyID:       cast.ToInt(queryResult.PolicyID),
				PolicyName:     queryResult.PolicyName,
				Archive:        queryResult.Archive,
				PolicyType:     queryResult.PolicyType,
				StandardType:   queryResult.StandardType,
				PendingCount:   queryResult.PendingCount,
				InProcessCount: queryResult.InProcessCount,
				CompletedCount: queryResult.CompletedCount,
				InvalidCount:   queryResult.InvalidCount,
			}
		} else {
			// 如果没有找到统计结果，则创建默认统计
			summary = &api.ReimbursementSummaryGroupByPolicyResp{
				PolicyID:       policy.ID,
				PolicyName:     policy.Name,
				Archive:        policy.Archive,
				PolicyType:     policy.PolicyType,
				StandardType:   policy.StandardType,
				PendingCount:   0,
				InProcessCount: 0,
				CompletedCount: 0,
				InvalidCount:   0,
			}
		}

		results = append(results, summary)
	}

	return results, nil
}

// MaterialSignIn 材料签收
func (r *repo) MaterialSignIn(c *gin.Context, orderID int, orderType string) (bool, error) {
	// 开始事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return false, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取状态列表
	orderList, err := r.getSummaryListByApplyOrderID(tx, orderID, orderType)
	if err != nil {
		tx.Rollback()
		return false, err
	}

	now := time.Now()

	// 更新主表
	var completionStatus int
	if len(orderList) == 0 {
		// 核销小单已作废等
		completionStatus = 1
	} else {
		// 检查是否存在未核销或核销中的订单
		hasUnfinished := false
		for _, status := range orderList {
			if status == 0 || status == 1 {
				hasUnfinished = true
				break
			}
		}

		if hasUnfinished {
			completionStatus = 0
		} else {
			completionStatus = 1
		}
	}

	// 更新主表
	var updateMainTable *gorm.DB
	if orderType == "promotional_products" {
		updateMainTable = tx.Model(&model.ReimbursementPromotionalProductsList{}).
			Where("id = ?", orderID).
			Updates(map[string]interface{}{
				"completion_status":      completionStatus,
				"completion_time":        now,
				"material_return_status": 2,
			})
	} else {
		updateMainTable = tx.Model(&model.ReimbursementAdvertExpenseList{}).
			Where("id = ?", orderID).
			Updates(map[string]interface{}{
				"completion_status":      completionStatus,
				"completion_time":        now,
				"material_return_status": 2,
			})
	}

	if err = updateMainTable.Error; err != nil {
		tx.Rollback()
		return false, err
	}

	// 更新汇总表
	if len(orderList) > 0 {
		hasUnfinished := false
		for _, status := range orderList {
			if status == 0 || status == 1 {
				hasUnfinished = true
				break
			}
		}

		if hasUnfinished {
			// 存在未核销或者核销中，更新状态>=0的记录
			err = tx.Model(&model.ReimbursementApplyOrderSummary{}).
				Where("apply_order_type = ? AND apply_order_id = ? AND status >= ?", orderType, orderID, 0).
				Updates(map[string]interface{}{
					"material_return_status": 2,
					"updated_at":             now,
				}).Error
		} else {
			// 已核销，更新状态=2的记录
			err = tx.Model(&model.ReimbursementApplyOrderSummary{}).
				Where("apply_order_type = ? AND apply_order_id = ? AND status = ?", orderType, orderID, 2).
				Updates(map[string]interface{}{
					"material_return_status": 2,
					"completion_status":      1,
					"completion_time":        now,
					"updated_at":             now,
				}).Error
		}

		if err != nil {
			tx.Rollback()
			return false, err
		}
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return false, err
	}

	return true, nil
}

// getSummaryListByApplyOrderID 根据申请单ID获取汇总列表
func (r *repo) getSummaryListByApplyOrderID(tx *gorm.DB, orderID int, orderType string) ([]int, error) {

	var status []int

	err := tx.Table("reimbursement_apply_order_summary").
		Select("status").
		Where("apply_order_id = ? AND apply_order_type = ?", orderID, orderType).
		Find(&status).Error

	return status, err
}

// GetPolicyByOrder 根据申请单获取政策信息
func (r *repo) GetPolicyByOrder(c *gin.Context, orderID int, orderType int) (*model.ReimbursementPolicy, error) {
	var policy model.ReimbursementPolicy
	now := time.Now()

	// 创建一个查询对象
	db := r.db.WithContext(c).Table("reimbursement_policy rp")

	// 根据申请单类型决定连接表
	switch orderType {
	case 1:
		db = db.Joins("INNER JOIN reimbursement_promotional_products_list ppl ON rp.id = ppl.policy_id")
		db = db.Where("ppl.id = ?", orderID)
	case 2:
		db = db.Joins("INNER JOIN reimbursement_advert_expense_list ael ON rp.id = ael.policy_id")
		db = db.Where("ael.id = ?", orderID)
	case 3:
		db = db.Joins("INNER JOIN reimbursement_apply_order_summary aos ON rp.id = aos.policy_id")
		db = db.Where("aos.id = ?", orderID)
	default:
		db = db.Joins("INNER JOIN reimbursement_list l ON rp.id = l.policy_id")
		db = db.Where("l.id = ?", orderID)
	}

	// 添加时间范围条件
	db = db.Where("rp.start_time <= ? AND rp.end_time >= ?", now, now)

	// 执行查询
	err := db.Select("rp.*").
		Scan(&policy).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &policy, nil
}

// GetSummaryShortcutStat 获取核销申请单汇总统计
func (r *repo) GetSummaryShortcutStat(c *gin.Context, req *api.SummaryShortcutStatReq, policyType string) (*api.ShortcutStatResp, error) {
	// 构建基础查询
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary s")

	// 根据政策类型选择不同的统计字段
	var selectFields string
	if policyType == "advert_expense" {
		selectFields = `COUNT(CASE WHEN s.status != -3 THEN 1 END) as total_count,
            COUNT(CASE WHEN s.status = 0 THEN 1 END) as pending_count,
            COUNT(CASE WHEN s.status = 1 THEN 1 END) as processing_count,
            COUNT(CASE WHEN s.status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN s.status = -100 THEN 1 END) as invalid_count,
            COUNT(CASE WHEN s.status = -3 THEN 1 END) as split_count,
            COUNT(CASE WHEN s.turn_type = 1 THEN 1 END) as transferred_count`
	} else {
		selectFields = `COUNT(CASE WHEN s.status != -3 THEN 1 END) as total_count,
            COUNT(CASE WHEN s.status = 0 THEN 1 END) as pending_count,
            COUNT(CASE WHEN s.status = 1 THEN 1 END) as processing_count,
            COUNT(CASE WHEN s.status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN s.status = -100 THEN 1 END) as invalid_count,
            COUNT(CASE WHEN s.status = -3 THEN 1 END) as split_count,
            COUNT(CASE WHEN s.status = -4 THEN 1 END) as transferred_count`
	}

	query = query.Select(selectFields)

	// 处理关联查询
	if req.ReimbursementListID != 0 {
		query = query.Joins("RIGHT JOIN reimbursement_list_summary_relation sr ON s.id = sr.reimbursement_summary_id").
			Where("s.policy_id = ? AND sr.reimbursement_list_id = ?", req.PolicyID, req.ReimbursementListID)
	} else {
		query = query.Where("s.policy_id = ?", req.PolicyID)
	}

	// 添加过滤条件
	if req.CompanyID != 0 {
		query = query.Where("s.company_id = ?", req.CompanyID)
	}

	if req.Status == nil || *req.Status != -3 {
		query = query.Where("s.status != -3")
	}

	if req.TopAgency != 0 {
		query = query.Where("s.top_agency = ?", req.TopAgency)
	}

	if req.ReimbursementStartTime != "" && req.ReimbursementEndTime != "" {
		query = query.Where("s.reimbursement_time BETWEEN ? AND ?",
			req.ReimbursementStartTime, req.ReimbursementEndTime)
	}

	if req.CompleteStartTime != "" && req.CompleteEndTime != "" {
		query = query.Where("s.completion_time BETWEEN ? AND ?",
			req.CompleteStartTime, req.CompleteEndTime)
	}

	if req.CompletionStatus != nil {
		query = query.Where("s.completion_status = ?", *req.CompletionStatus)
	}

	// 执行查询
	var result api.ShortcutStatResp
	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// GetSummaryList 获取核销小单列表
func (r *repo) GetSummaryList(c *gin.Context, req *api.SummaryShortcutStatReq) ([]*api.SummaryListItem, int64, error) {
	// 定义查询字段
	selectFields := []string{
		"rs.id",
		"rs.code",
		"rs.apply_order_id",
		"rs.company_id",
		"rs.company",
		"rs.amount",
		"rs.actual_amount",
		"rs.reimbursement_apply_amount",
		"IF(rs.status = 0, rs.amount, 0) as no_reimbursement_amount",
		"IF(rs.turn_type = 1, rs.amount, 0) as turn_amount",
		"rs.quantity_total",
		"rs.actual_quantity",
		"rs.top_agency",
		"a.name as agency_name",
		"rs.policy_id",
		"rs.apply_order_type",
		"rs.audit_time",
		"rs.reimbursement_type",
		"rp.name as policy_name",
		"rs.status",
		"rs.completion_status",
		"rs.created_at",
		"rs.reimbursement_time",
		"rs.completion_time",
		"rs.turn_type",
		"rs.split_type",
		"rlsr.reimbursement_list_id",
		"rs.order_id",
		"rp.standard_type",
		"rs.invalid_amount",
		"rs.material_return_status",
		"rs.invalid_remark",
	}

	// 构建基础查询
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary rs").
		Joins("LEFT JOIN agency a ON rs.top_agency = a.id").
		Joins("LEFT JOIN reimbursement_policy rp ON rs.policy_id = rp.id").
		Joins("LEFT JOIN reimbursement_list_summary_relation rlsr on rs.id = rlsr.reimbursement_summary_id")

	if req.ReimbursementListID != 0 {
		// 列表ID存在时使用关联查询
		query = r.buildRelationQuery(query, req)
		selectFields = []string{
			"rs.id",
			"rs.code",
			"rs.apply_order_id",
			"rs.company_id",
			"rs.company",
			"rs.amount",
			"rs.actual_amount",
			"rs.reimbursement_apply_amount",
			"IF(rs.status = 0, rs.amount, 0) as no_reimbursement_amount",
			"IF(rs.turn_type = 1, rs.amount, 0) as turn_amount",
			"rs.quantity_total",
			"rs.actual_quantity",
			"rs.top_agency",
			"a.name as agency_name",
			"rs.policy_id",
			"rp.name as policy_name",
			"rs.status",
			"rs.completion_status",
			"rs.created_at",
			"rs.reimbursement_time",
			"rs.completion_time",
			"rs.audit_time",
			"rs.turn_type",
			"rs.invalid_amount",
			"rs.material_return_status",
			"rs.invalid_remark",
			"rlsr.reimbursement_list_id",
		}
	} else {
		// 普通列表查询
		query = r.buildNormalQuery(query, req)
	}
	query = query.Select(selectFields)

	// 获取总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	var items []*api.SummaryListItem
	err = query.Order("rs.id DESC").
		Limit(req.PageSize).
		Offset((req.Page - 1) * req.PageSize).
		Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// buildRelationQuery 构建关联查询
func (r *repo) buildRelationQuery(query *gorm.DB, req *api.SummaryShortcutStatReq) *gorm.DB {
	query = query.Where("rlsr.reimbursement_list_id = ?", req.ReimbursementListID)

	// 添加策略ID条件
	if req.PolicyID != 0 {
		query = query.Where("rs.policy_id = ?", req.PolicyID)
	}

	// 处理状态条件
	query = r.buildStatusCondition(query, req)

	// 添加其他通用条件
	return r.buildCommonConditions(query, req)
}

// buildNormalQuery 构建普通查询
func (r *repo) buildNormalQuery(query *gorm.DB, req *api.SummaryShortcutStatReq) *gorm.DB {
	// 添加策略ID条件
	if req.PolicyID != 0 {
		query = query.Where("rs.policy_id = ?", req.PolicyID)
	}

	// 处理状态条件
	query = r.buildStatusCondition(query, req)

	// 添加其他通用条件
	return r.buildCommonConditions(query, req)
}

// buildStatusCondition 构建状态查询条件
func (r *repo) buildStatusCondition(query *gorm.DB, req *api.SummaryShortcutStatReq) *gorm.DB {
	if req.PolicyType == "advert_expense" {
		if req.Status != nil && *req.Status == -4 {
			query = query.Where("rs.turn_type = 1")
		} else if req.Status != nil {
			query = query.Where("rs.status = ?", req.Status)
		}
	} else {
		if req.Status != nil {
			query = query.Where("rs.status = ?", req.Status)
		}
	}

	// 默认不显示已拆分记录
	if req.Status == nil || *req.Status != -3 {
		query = query.Where("rs.status != -3")
	}

	return query
}

// buildCommonConditions 构建通用查询条件
func (r *repo) buildCommonConditions(query *gorm.DB, req *api.SummaryShortcutStatReq) *gorm.DB {
	if req.TopAgency != 0 {
		query = query.Where("rs.top_agency = ?", req.TopAgency)
	}

	if req.ReimbursementStartTime != "" && req.ReimbursementEndTime != "" {
		query = query.Where("rs.reimbursement_time BETWEEN ? AND ?",
			req.ReimbursementStartTime, req.ReimbursementEndTime)
	}

	if req.CompleteStartTime != "" && req.CompleteEndTime != "" {
		query = query.Where("rs.completion_time BETWEEN ? AND ?",
			req.CompleteStartTime, req.CompleteEndTime)
	}

	if req.CompletionStatus != nil {
		query = query.Where("rs.completion_status = ?", req.CompletionStatus)
	}

	if req.CompanyID != 0 {
		query = query.Where("rs.company_id = ?", req.CompanyID)
	}

	return query
}

// ReimbursementApplyOrder 获取核销申请单信息
func (r *repo) ReimbursementApplyOrder(c *gin.Context, orderID int) (*model.ReimbursementApplyOrderSummary, error) {
	var order model.ReimbursementApplyOrderSummary

	err := r.db.WithContext(c).
		Select([]string{
			"id",
			"uid",
			"apply_order_id",
			"uid",
			"status",
			"reimbursement_type",
			"top_agency",
			"second_agency",
			"company_id",
			"code",
			"company",
			"policy_id",
			"amount",
			"quantity_total",
			"apply_order_type",
			"split_type",
			"turn_type",
			"material_return_status",
		}).
		Table("reimbursement_apply_order_summary").
		Where("id = ?", orderID).
		First(&order).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

func (r *repo) GetOrdersDetail(c *gin.Context, companyID int, ordersID []int) ([]model.ReimbursementApplyOrderSummary, error) {

	var details []model.ReimbursementApplyOrderSummary

	// 查询对应小单
	err := r.db.WithContext(c).
		Table("reimbursement_apply_order_summary").
		Where("id IN ? AND company_id = ?", ordersID, companyID).
		Find(&details).Error
	if err != nil {
		return nil, err
	}
	return details, nil
}

func (r *repo) ReimbursementCreate(c *gin.Context, req *api.ApplyOrderSummarySubmitReq, policy *model.ReimbursementPolicy, orders []*OrderItem, balance *model.ReimbursementBalanceStandard) error {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		now := time.Now()
		sn := utils.GenerateUniqueSN()

		// 1. 创建核销大单
		reimbursementList := &model.ReimbursementList{
			SN:                       sn,
			PolicyID:                 req.PolicyID,
			ReimbursementType:        policy.ReimbursementType,
			ReimbursementAmount:      req.ReimbursementAmount,      //实际的核销金额，比如促销品以前都是半价核销 这里取核销的一半 后面改成自定义核销金额（旧页面逻辑都是前端计算）
			Amount:                   req.Amount,                   //核销的额度或者是促销品的总价格
			ReimbursementApplyAmount: req.ReimbursementApplyAmount, //促销品可以自定义核销单价（那相应的界面核销额度不能只取amount一半）
			Quantity:                 req.Quantity,
			ReimbursementQuantity:    req.ReimbursementQuantity, //实际核销数量
			UID:                      req.UID,
			TopAgency:                req.TopAgency,
			CompanyID:                req.CompanyID,
			Code:                     req.Code,
			Company:                  req.Company,
			Status:                   1,
			CreatedAt:                now,
		}

		if err := tx.Create(reimbursementList).Error; err != nil {
			return err
		}

		// 2. 处理每个订单
		excessReduceAdvertExpenseAmount := 0.0
		reduceAdvertExpenseAmount := 0.0

		for _, order := range orders {
			// 创建大单和小单关联
			relation := &model.ReimbursementListSummaryRelation{
				ReimbursementListID:    reimbursementList.ID,
				ReimbursementSummaryID: order.ID,
			}
			if err := tx.Create(relation).Error; err != nil {
				return err
			}

			// 更新小单状态
			updates := map[string]interface{}{
				"status":             1,
				"actual_amount":      order.Amount,
				"actual_quantity":    order.Quantity,
				"reimbursement_time": now,
				"updated_at":         now,
			}
			if err := tx.Model(&model.ReimbursementApplyOrderSummary{}).
				Where("id = ? AND status = 0", order.ID).
				Updates(updates).Error; err != nil {
				return err
			}

			// 计算额度
			if req.StandardType == "standard_balance_quota" {
				if order.TurnType == 1 {
					excessReduceAdvertExpenseAmount += order.Amount
				} else {
					reduceAdvertExpenseAmount += order.Amount
				}
			}
		}

		// 3. 创建标准变更记录
		orderIDs := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(req.IDs)), ","), "[]")

		balanceStandard := &model.ReimbursementBalanceStandard{
			UID:          req.UID,
			OrderID:      reimbursementList.ID,
			OrdersID:     orderIDs,
			PolicyID:     req.PolicyID,
			CompanyID:    cast.ToUint(req.CompanyID),
			Code:         req.Code,
			Company:      req.Company,
			TopAgency:    cast.ToUint(req.TopAgency),
			SecondAgency: 0,
			CreatedAt:    now,
			Type:         3,
			Remark:       "核销确认",
			Month:        now,
		}

		// 根据不同标准类型处理额度
		switch req.StandardType {
		case "standard_amount":
			balanceStandard.NormQuantity = -req.ReimbursementAmount
			balanceStandard.Balance = balance.Balance - req.ReimbursementAmount
			balanceStandard.StandardType = "standard_amount"

		case "standard_quantity":
			// 处理数量标准
			quantityStandard := *balanceStandard
			quantityStandard.NormQuantity = float64(-req.Quantity)
			quantityStandard.Balance = balance.Balance - float64(req.ReimbursementQuantity)
			quantityStandard.StandardType = "standard_quantity"
			if err := tx.Create(&quantityStandard).Error; err != nil {
				return err
			}

			// 处理余额标准
			balanceStandard.NormQuantity = req.ReimbursementAmount
			balanceStandard.Balance = balance.Balance + req.ReimbursementAmount
			balanceStandard.StandardType = "standard_balance_quota"

		default:
			balanceStandard.NormQuantity = -req.Amount
			balanceStandard.Balance = balance.Balance - req.ReimbursementAmount
			balanceStandard.StandardType = req.StandardType
		}

		if err := tx.Create(balanceStandard).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *repo) GetOrderDetail(c *gin.Context, orderID int) (*model.ReimbursementList, error) {
	var detail model.ReimbursementList

	// 使用 GORM 查询
	err := r.db.WithContext(c).
		Table("reimbursement_list").
		Select("id, sn, policy_id, reimbursement_type, reimbursement_amount, top_agency, company_id, code, company, status, reimbursement_amount, reimbursement_quantity").
		Where("id = ?", orderID).
		First(&detail).Error

	if err != nil {
		return nil, err
	}

	return &detail, nil
}

func (r *repo) GetReimbursementPolicy(c *gin.Context, policyID int) (*model.ReimbursementPolicy, error) {
	var policy model.ReimbursementPolicy

	// 使用 GORM 查询
	err := r.db.WithContext(c).
		Table("reimbursement_policy").
		Select("id, name, start_time, end_time, reimbursement_type, standard_type, policy_type, type, archive, declare").
		Where("id = ?", policyID).
		First(&policy).Error

	if err != nil {
		return nil, err
	}

	return &policy, nil
}

func (r *repo) ReimbursementRetrial(c *gin.Context, uid uint, reimbursementID int, summaryIDs []int, order *model.ReimbursementList, policy *model.ReimbursementPolicy, balance *model.ReimbursementBalanceStandard) error {
	now := time.Now()

	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		var actualReimbursementAmount, amount float64
		var excessReduceAdvertExpenseAmount, reduceAdvertExpenseAmount float64
		var quantity, reimbursementQuantity int64

		// 查询关联的小单
		var reimbursementSummaryIDs []int
		err := tx.Table("reimbursement_list_summary_relation").
			Select("reimbursement_summary_id").
			Where("reimbursement_list_id = ?", reimbursementID).
			Find(&reimbursementSummaryIDs).Error
		if err != nil {
			return err
		}

		for _, summaryID := range summaryIDs {
			var summaryOrder model.ReimbursementApplyOrderSummary
			err := tx.Table("reimbursement_apply_order_summary AS raos").
				Joins("RIGHT JOIN reimbursement_list_summary_relation rlsr ON raos.id = rlsr.reimbursement_summary_id").
				Where("rlsr.reimbursement_summary_id = ? AND rlsr.reimbursement_list_id = ?", summaryID, reimbursementID).
				First(&summaryOrder).Error
			if err != nil {
				return fmt.Errorf("未找到此小单信息, 请确认是否为此大单下的小单")
			}
			if summaryOrder.Status != 1 {
				return fmt.Errorf("订单（id %d）不是已确认状态，无法反审", summaryID)
			}

			// 根据政策类型计算金额
			if policy.StandardType == "standard_balance_quota" {
				if summaryOrder.TurnType == 1 {
					excessReduceAdvertExpenseAmount += summaryOrder.ActualAmount
				} else {
					reduceAdvertExpenseAmount += summaryOrder.ActualAmount
				}
			}

			actualReimbursementAmount += summaryOrder.ActualAmount
			amount += summaryOrder.Amount
			if policy.StandardType == "standard_quantity" {
				quantity += summaryOrder.QuantityTotal
				reimbursementQuantity += summaryOrder.ActualQuantity
			}

			// 删除大单和小单关联
			err = tx.Table("reimbursement_list_summary_relation").
				Where("reimbursement_list_id = ? AND reimbursement_summary_id = ?", reimbursementID, summaryID).
				Delete(nil).Error
			if err != nil {
				return err
			}

			// 更新小单为待核销状态
			err = tx.Table("reimbursement_apply_order_summary").
				Where("id = ? AND status = 1", summaryID).
				Updates(map[string]interface{}{
					"status":             0,
					"actual_amount":      0,
					"actual_quantity":    0,
					"reimbursement_time": nil,
					"invalid_amount":     0,
					"updated_at":         now,
				}).Error
			if err != nil {
				return err
			}
		}

		// 如果核销小单数等于上传的数，则作废大单
		if len(reimbursementSummaryIDs) == len(summaryIDs) {
			err = tx.Table("reimbursement_list").
				Where("id = ?", reimbursementID).
				Updates(map[string]interface{}{
					"status":     -100,
					"remark":     "全部小单反审, 大单作废",
					"updated_at": now,
				}).Error
			if err != nil {
				return err
			}
		} else {
			// 大单减去核销金额或核销数量
			err = tx.Table("reimbursement_list").
				Where("id = ?", reimbursementID).
				Updates(map[string]interface{}{
					"reimbursement_amount":   gorm.Expr("reimbursement_amount - ?", actualReimbursementAmount),
					"amount":                 gorm.Expr("amount - ?", amount),
					"quantity":               gorm.Expr("quantity - ?", quantity),
					"reimbursement_quantity": gorm.Expr("reimbursement_quantity - ?", reimbursementQuantity),
				}).Error
			if err != nil {
				return err
			}
		}

		// 减核销标准并添加操作记录
		balanceAmount := balance.Balance + actualReimbursementAmount
		balanceQuantity := cast.ToInt64(balance.Balance) + reimbursementQuantity

		record := model.ReimbursementBalanceStandard{
			UID:          uid,
			OrderID:      reimbursementID,
			OrdersID:     strings.Join(strings.Fields(fmt.Sprint(summaryIDs)), ","),
			PolicyID:     policy.ID,
			CompanyID:    cast.ToUint(order.CompanyID),
			Code:         order.Code,
			Company:      order.Company,
			TopAgency:    cast.ToUint(order.TopAgency),
			NormQuantity: cast.ToFloat64(quantity),
			Balance:      balanceAmount,
			Month:        now,
			StandardType: policy.StandardType,
			Remark:       "核销反审，核销标准回退",
			CreatedAt:    now,
			Type:         4,
		}

		if policy.StandardType == "standard_quantity" {
			record.Balance = cast.ToFloat64(balanceQuantity)
		}

		err = tx.Create(&record).Error
		if err != nil {
			return err
		}

		return nil
	})
}

func (r *repo) ReimbursementAudit(c *gin.Context, req *api.ReimbursementAuditReq, order *model.ReimbursementList, policyInfo *model.ReimbursementPolicy, balance *model.ReimbursementBalanceStandard) error {
	now := time.Now()

	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新大单状态
		err := tx.Table("reimbursement_list").
			Where("id = ?", req.ID).
			Updates(map[string]interface{}{
				"status":     req.Status,
				"audit_man":  req.Uid,
				"audit_time": now,
				"remark":     req.Remark,
			}).Error
		if err != nil {
			return err
		}

		// 更新小单状态
		err = tx.Table("reimbursement_apply_order_summary AS rs").
			Joins("LEFT JOIN reimbursement_list_summary_relation rlsr ON rs.id = rlsr.reimbursement_summary_id").
			Where("rlsr.reimbursement_list_id = ?", req.ID).
			Updates(map[string]interface{}{
				"status":     req.Status,
				"audit_man":  req.Uid,
				"audit_time": now,
				"remark":     req.Remark,
				"updated_at": now,
			}).Error
		if err != nil {
			return err
		}

		// 如果审核失败，回退支持标准
		if req.Status == -1 {
			var balanceAmount float64

			switch policyInfo.StandardType {
			case "standard_amount":
				balanceAmount = balance.Balance + order.ReimbursementAmount
			case "standard_quantity":
				balanceAmount = float64(balance.Balance) + float64(order.ReimbursementQuantity)
			default:
				balanceAmount = balance.Balance + order.ReimbursementAmount
			}

			record := model.ReimbursementBalanceStandard{
				UID:          req.Uid,
				OrderID:      req.ID,
				OrdersID:     strings.Join(strings.Fields(fmt.Sprint(req.ID)), ","),
				PolicyID:     policyInfo.ID,
				CompanyID:    cast.ToUint(order.CompanyID),
				Code:         order.Code,
				Company:      order.Company,
				TopAgency:    cast.ToUint(order.TopAgency),
				NormQuantity: cast.ToFloat64(order.ReimbursementQuantity),
				Balance:      balanceAmount,
				Month:        now,
				StandardType: policyInfo.StandardType,
				Remark:       "核销领导审核不通过, 支持标准回退",
				CreatedAt:    now,
				Type:         4,
			}

			err = tx.Create(&record).Error
			if err != nil {
				return err
			}
		} else {
			// 更新小单完成状态
			err = tx.Table("reimbursement_apply_order_summary AS rs").
				Joins("LEFT JOIN reimbursement_list_summary_relation rlsr ON rs.id = rlsr.reimbursement_summary_id").
				Where("rlsr.reimbursement_list_id = ? AND rs.material_return_status = ?", req.ID, 2).
				Updates(map[string]interface{}{
					"completion_status": 1,
					"completion_time":   now,
					"updated_at":        now,
				}).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// GetReimbursementAllSummaryTotal 获取核销列表统计
func (r *repo) GetReimbursementAllSummaryTotal(c *gin.Context, req *api.CompanySummaryReq) (*api.CompanySummaryStatResp, error) {
	var result api.CompanySummaryStatResp

	// 构建基础查询
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Select(`
            ROUND(SUM(IF(raos.status != -3, raos.amount, 0)), 2) as amount,
            ROUND(SUM(IF(rp.policy_type = ?, raos.amount, 
                IF(raos.reimbursement_apply_amount, raos.reimbursement_apply_amount, raos.amount/2))), 2) as half_amount,
            SUM(IF(raos.status > 0, raos.actual_amount, 0)) as actual_amount,
            SUM(IF(raos.status > 0, raos.actual_quantity, 0)) as actual_quantity,
            SUM(IF(raos.status != -3, raos.quantity_total, 0)) as quantity_total,
            SUM(IF(raos.status != -3, IF(raos.reimbursement_apply_amount, raos.reimbursement_apply_amount, raos.quantity_total), 0)) as half_quantity_total,
            SUM(IF(raos.status = 0, IF(rp.policy_type = ?, raos.amount, IF(raos.reimbursement_apply_amount, raos.reimbursement_apply_amount, raos.amount/2)), 0)) as no_reimbursement_amount,
            SUM(IF(raos.status = -4, raos.amount, 0)) as turn_amount,
            SUM(IF(raos.status = -100, raos.amount, 0) + IF(raos.status = 1 or raos.status = 2, raos.invalid_amount, 0)) as invalid_amount,
            SUM(IF(raos.status > 0 and rp.standard_type = 'standard_balance_quota', raos.actual_amount, 0)) as reduce_advert_expense_amount,
            SUM(IF(raos.status > 0 and rp.standard_type = 'standard_balance_quota', raos.actual_amount, 0)) as ae_increase_discount_amount
        `, "advert_expense", "advert_expense").
		Joins("LEFT JOIN reimbursement_policy rp ON raos.policy_id = rp.id")

	// 构建WHERE条件
	if req.PolicyType == "advert_expense" {
		query = query.Where("raos.policy_id = ?", req.PolicyID)
	} else {
		query = query.Where("raos.policy_id = ? AND raos.turn_type = 0", req.PolicyID)
	}

	if req.TopAgency != 0 {
		query = query.Where("raos.top_agency = ?", req.TopAgency)
	}
	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("raos.updated_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}
	if req.CompanyID != 0 {
		query = query.Where("raos.company_id = ?", req.CompanyID)
	}
	if req.Code != "" {
		query = query.Where("raos.code = ?", req.Code)
	}

	// 执行第一个查询
	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	// 执行第二个查询获取标准值
	var balance struct {
		Balance float64 `gorm:"column:balance"`
	}
	balanceQuery := r.db.WithContext(c).Table("reimbursement_balance_import_standard r1").
		Select("SUM(r1.balance) as balance").
		Joins(`RIGHT JOIN (
            SELECT max(rbis.id) id 
            FROM reimbursement_balance_import_standard rbis 
            RIGHT JOIN (
                SELECT DISTINCT code 
                FROM reimbursement_apply_order_summary 
                WHERE policy_id = ?
        ) raos ON raos.code = rbis.code 
        WHERE rbis.policy_id = ? AND rbis.standard_type = ? 
        GROUP BY company_id, policy_id) a ON r1.id = a.id`, req.PolicyID, req.PolicyID, req.StandardType)

	// 添加额外的查询条件
	if req.TopAgency != 0 {
		balanceQuery = balanceQuery.Where("top_agency = ?", req.TopAgency)
	}
	if req.StartTime != "" && req.EndTime != "" {
		balanceQuery = balanceQuery.Where("updated_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}
	if req.CompanyID != 0 {
		balanceQuery = balanceQuery.Where("company_id = ?", req.CompanyID)
	}
	if req.Code != "" {
		balanceQuery = balanceQuery.Where("code = ?", req.Code)
	}

	err = balanceQuery.Scan(&balance).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 设置标准值
	switch req.StandardType {
	case "standard_balance_quota":
		result.StandardBalanceQuota = balance.Balance
	case "standard_amount":
		result.StandardAmount = balance.Balance
	case "standard_quantity":
		result.StandardQuantity = balance.Balance
	}

	// 处理特殊计算
	if req.PolicyType != "advert_expense" && result.InvalidAmount != 0 {
		result.InvalidAmount = result.InvalidAmount / 2
	}
	if result.ReduceAdvertExpenseAmount != 0 {
		result.ReduceAdvertExpenseAmount = -result.ReduceAdvertExpenseAmount
	}

	return &result, nil
}

func (r *repo) GetReimbursementAllSummaryPage(c *gin.Context, req *api.CompanySummaryReq) ([]*api.ReimbursementSummaryPageResp, int64, error) {
	baseQuery := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Joins("LEFT JOIN agency a ON raos.top_agency = a.id").
		Joins("LEFT JOIN reimbursement_policy rp ON raos.policy_id = rp.id")

	countQuery := baseQuery

	if req.PolicyType == "advert_expense" {
		baseQuery = baseQuery.Where("raos.policy_id = ?", req.PolicyID)
		countQuery = countQuery.Where("raos.policy_id = ?", req.PolicyID)
	} else {
		baseQuery = baseQuery.Where("raos.policy_id = ? AND raos.turn_type = 0", req.PolicyID)
		countQuery = countQuery.Where("raos.policy_id = ? AND raos.turn_type = 0", req.PolicyID)
	}

	if req.TopAgency != 0 {
		baseQuery = baseQuery.Where("raos.top_agency = ?", req.TopAgency)
		countQuery = countQuery.Where("raos.top_agency = ?", req.TopAgency)
	}
	if req.StartTime != "" && req.EndTime != "" {
		baseQuery = baseQuery.Where("raos.updated_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
		countQuery = countQuery.Where("raos.updated_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}
	if req.CompanyID != 0 {
		baseQuery = baseQuery.Where("raos.company_id = ?", req.CompanyID)
		countQuery = countQuery.Where("raos.company_id = ?", req.CompanyID)
	}
	if req.Code != "" {
		baseQuery = baseQuery.Where("raos.code = ?", req.Code)
		countQuery = countQuery.Where("raos.code = ?", req.Code)
	}

	var total int64
	err := countQuery.Group("raos.company_id").Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if total == 0 {
		return make([]*api.ReimbursementSummaryPageResp, 0), 0, nil
	}

	offset := (req.Page - 1) * req.PageSize

	// 修正后的SELECT语句（特别注意invalid_amount的表达式）
	selectSQL := `
        raos.code, 
        raos.top_agency, 
        raos.company_id, 
        raos.company, 
        rp.standard_type,
        a.name AS agency_name,
        IF(rp.standard_type = 'standard_amount', 
            (SELECT s.balance FROM reimbursement_balance_import_standard s 
             WHERE s.standard_type = 'standard_amount' AND s.policy_id = raos.policy_id 
             AND s.company_id = raos.company_id ORDER BY s.created_at DESC LIMIT 1), 
            0) AS standard_amount,
        IF(rp.standard_type = 'standard_quantity', 
            (SELECT s.balance FROM reimbursement_balance_import_standard s 
             WHERE s.standard_type = 'standard_quantity' AND s.policy_id = raos.policy_id 
             AND s.company_id = raos.company_id ORDER BY s.created_at DESC LIMIT 1), 
            0) AS standard_quantity,
        IF(rp.standard_type = 'standard_balance_quota', 
            (SELECT s.balance FROM reimbursement_balance_import_standard s 
             WHERE s.standard_type = 'standard_balance_quota' AND s.policy_id = raos.policy_id 
             AND s.company_id = raos.company_id ORDER BY s.created_at DESC LIMIT 1), 
            0) AS standard_balance_quota,
        SUM(IF(raos.status > 0 AND rp.standard_type = 'standard_balance_quota', 
            raos.actual_amount, 0)) AS reduce_advert_expense_amount,
        SUM(IF(raos.status > 0 AND rp.standard_type = 'standard_balance_quota', 
            raos.actual_amount, 0)) AS ae_increase_discount_amount,
        ROUND(SUM(IF(raos.status != -3, raos.amount, 0)), 2) AS amount,
        ROUND(SUM(IF(rp.policy_type = 'advert_expense', raos.amount, 
            IF(raos.reimbursement_apply_amount, raos.reimbursement_apply_amount, raos.amount/2))), 2) AS half_amount,
        SUM(IF(raos.status > 0, raos.actual_amount, 0)) AS actual_amount,
        SUM(IF(raos.status > 0, raos.actual_quantity, 0)) AS actual_quantity,
        SUM(IF(raos.status != -3, raos.quantity_total, 0)) AS quantity_total,
        SUM(IF(raos.status != -3, raos.quantity_total, 0)) AS half_quantity_total,
        SUM(IF(raos.status = 0, 
            IF(rp.policy_type = 'advert_expense', raos.amount, 
                IF(raos.reimbursement_apply_amount, raos.reimbursement_apply_amount, raos.amount/2)), 0)) AS no_reimbursement_amount,
        SUM(IF(raos.status = -4, raos.amount, 0)) AS turn_amount,
        SUM(IF(raos.status = -100, raos.amount, 0) + IF(raos.status = 1 or raos.status=2, raos.invalid_amount, 0)) AS invalid_amount,
        raos.reimbursement_type,
        MAX(raos.updated_at) AS updated_at
    `

	var result []*api.ReimbursementSummaryPageResp
	err = baseQuery.Select(selectSQL).
		Group("raos.company_id").
		Limit(req.PageSize).
		Offset(offset).
		Scan(&result).Error

	if err != nil {
		return nil, 0, err
	}

	for _, item := range result {
		if !item.UpdatedAt.IsZero() {
			item.UpdatedAtStr = item.UpdatedAt.Format("2006-01-02 15:04:05")
		}
		if req.PolicyType != "advert_expense" && item.InvalidAmount != 0 {
			item.InvalidAmount = item.InvalidAmount / 2
		}
	}

	return result, total, nil
}

func (r *repo) GetPendingReimbursementList(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error) {
	var results []api.PendingTask

	err := r.db.WithContext(c).
		Table("reimbursement_list rl").
		Select(`rp.id AS policy_id,
                rp.name AS policy_name,
                rp.policy_type,
                rl.id,
                rl.sn,
                rl.status,
                rl.created_at,
                rl.company,
                CASE WHEN rp.type = 3 THEN rl.amount ELSE rl.amount/2 END as amount,
                ? as type`, 2).
		Joins("LEFT JOIN reimbursement_policy rp ON rl.policy_id = rp.id").
		Where("(rl.status = ? OR rl.status = ?) AND rl.top_agency = ? AND rl.company_id = ? AND rl.confirm_status = ?",
			1, 2, topAgency, companyID, 0).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

// GetClientSummary 获取客户端汇总数据
func (r *repo) GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, int64, error) {
	// 如果channel为空，先获取代理商信息
	if req.Channel == "" {
		var agencyInfo struct {
			Channel string `json:"channel"`
		}
		err := r.db.WithContext(c).Table("agency").
			Select("channel").
			Where("id = ?", req.TopAgency).
			Scan(&agencyInfo).Error

		if err != nil {
			return nil, 0, err
		}
		req.Channel = agencyInfo.Channel
	}

	// 根据渠道类型确定look_over参数
	var lookOverParams []string
	if req.Channel == "e_commerce" {
		lookOverParams = []string{"all", "online"}
	} else {
		lookOverParams = []string{"all", "traditional"}
	}

	// 获取政策列表
	var policies []*model.ReimbursementPolicy
	var total int64

	query := r.db.WithContext(c).Table("reimbursement_policy rp").
		Select("rp.id, rp.name, rp.policy_type, rp.archive").
		Where("rp.archive = ? ", req.Archive).
		Where("rp.type != ? AND (rp.look_over = ? OR rp.look_over = ?)", 4, lookOverParams[0], lookOverParams[1])
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Order("rp.id DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Scan(&policies).Error

	if err != nil {
		return nil, 0, err
	}

	if len(policies) == 0 {
		return []*api.ClientSummaryResp{}, 0, nil
	}

	// 提取政策ID列表用于批量查询
	var promotionalIDs, advertIDs []int
	policyMap := make(map[int]*api.ClientSummaryResp)

	for _, policy := range policies {
		if policy.PolicyType == "promotional_products" {
			promotionalIDs = append(promotionalIDs, policy.ID)
		} else {
			advertIDs = append(advertIDs, policy.ID)
		}
		policyMap[policy.ID] = &api.ClientSummaryResp{
			ID:         policy.ID,
			Name:       policy.Name,
			PolicyType: policy.PolicyType,
			Archive:    policy.Archive,
		}
	}

	// 批量查询推广产品申请统计
	var promotionalStats []struct {
		PolicyID    int `json:"policy_id"`
		Applied     int `json:"applied"`
		UnderUpload int `json:"under_upload"`
		UnderAudit  int `json:"under_audit"`
	}

	err = r.db.WithContext(c).Table("reimbursement_promotional_products_list").
		Select(`
			policy_id,
			IFNULL(SUM(status=0 OR status=-3), 0) AS applied,
			IFNULL(SUM(status=1), 0) AS under_upload,
			IFNULL(SUM(status=2), 0) AS under_audit
		`).
		Where("policy_id IN ? AND company_id = ? AND top_agency = ?", promotionalIDs, req.CompanyID, req.TopAgency).
		Group("policy_id").
		Scan(&promotionalStats).Error

	if err != nil {
		return nil, 0, err
	}

	// 应用推广产品统计数据
	for _, stat := range promotionalStats {
		if result, exists := policyMap[stat.PolicyID]; exists {
			result.Applied = stat.Applied
			result.UnderUpload = stat.UnderUpload
			result.UnderAudit = stat.UnderAudit
		}
	}

	// 批量查询广告费用申请统计
	var advertStats []struct {
		PolicyID int `json:"policy_id"`
		Applied  int `json:"applied"`
	}

	err = r.db.WithContext(c).Table("reimbursement_advert_expense_list").
		Select(`
			policy_id,
			IFNULL(SUM(status=0 OR status=-1), 0) AS applied
		`).
		Where("policy_id IN ? AND company_id = ? AND top_agency = ?", advertIDs, req.CompanyID, req.TopAgency).
		Group("policy_id").
		Scan(&advertStats).Error

	if err != nil {
		return nil, 0, err
	}

	// 应用广告费用统计数据
	for _, stat := range advertStats {
		if result, exists := policyMap[stat.PolicyID]; exists {
			result.Applied = stat.Applied
		}
	}

	// 批量查询推广产品核销汇总统计
	var promotionalSummaryStats []struct {
		PolicyID    int `json:"policy_id"`
		NoReimburse int `json:"no_reimburse"`
	}

	err = r.db.WithContext(c).Table("reimbursement_apply_order_summary").
		Select(`
			policy_id,
			IFNULL(SUM(status=0 OR status=-100), 0) AS no_reimburse
		`).
		Where("policy_id IN ? AND company_id = ? AND top_agency = ?", promotionalIDs, req.CompanyID, req.TopAgency).
		Group("policy_id").
		Scan(&promotionalSummaryStats).Error

	if err != nil {
		return nil, 0, err
	}

	// 应用推广产品核销汇总统计数据
	for _, stat := range promotionalSummaryStats {
		if result, exists := policyMap[stat.PolicyID]; exists {
			result.NoReimburse = stat.NoReimburse
		}
	}

	// 批量查询广告费用核销汇总统计（包含turn_type=1的特殊逻辑）
	var advertSummaryStats []struct {
		PolicyID    int `json:"policy_id"`
		NoReimburse int `json:"no_reimburse"`
	}

	err = r.db.WithContext(c).Table("reimbursement_apply_order_summary").
		Select(`
			CASE WHEN turn_type = 1 THEN 0 ELSE policy_id END as policy_id,
			IFNULL(SUM(status=0 OR status=-100), 0) AS no_reimburse
		`).
		Where("((policy_id IN ? AND turn_type = 0) OR turn_type = 1) AND company_id = ? AND top_agency = ?", advertIDs, req.CompanyID, req.TopAgency).
		Group("CASE WHEN turn_type = 1 THEN 0 ELSE policy_id END").
		Scan(&advertSummaryStats).Error

	if err != nil {
		return nil, 0, err
	}

	// 应用广告费用核销汇总统计数据（包括turn_type=1的数据）
	for _, stat := range advertSummaryStats {
		if stat.PolicyID == 0 {
			// turn_type=1的数据需要应用到所有广告费用政策
			for _, policy := range policies {
				if policy.PolicyType == "advert_expense" {
					if result, exists := policyMap[policy.ID]; exists {
						result.NoReimburse += stat.NoReimburse
					}
				}
			}
		} else {
			if result, exists := policyMap[stat.PolicyID]; exists {
				result.NoReimburse += stat.NoReimburse
			}
		}
	}

	// 批量查询核销列表统计
	var reimbursementStats []struct {
		PolicyID   int `json:"policy_id"`
		NoReturn   int `json:"no_return"`
		Reimbursed int `json:"reimbursed"`
	}
	policyIDs := append(promotionalIDs, advertIDs...)
	err = r.db.WithContext(c).Table("reimbursement_list").
		Select(`
			policy_id,
			IFNULL(SUM((status=1 OR status=2) AND confirm_status=0), 0) AS no_return,
			IFNULL(SUM(status=2), 0) AS reimbursed
		`).
		Where("policy_id IN ? AND company_id = ? AND top_agency = ?", policyIDs, req.CompanyID, req.TopAgency).
		Group("policy_id").
		Scan(&reimbursementStats).Error

	if err != nil {
		return nil, 0, err
	}

	// 应用核销列表统计数据
	for _, stat := range reimbursementStats {
		if result, exists := policyMap[stat.PolicyID]; exists {
			result.NoReturn = stat.NoReturn
			result.Reimbursed = stat.Reimbursed
		}
	}

	// 构建结果列表，保持原有顺序
	var results []*api.ClientSummaryResp
	for _, policy := range policies {
		results = append(results, policyMap[policy.ID])
	}

	return results, total, nil
}

func (r *repo) GetPolicyCompanySummary(c *gin.Context, policyID, companyID int, topAgency uint) (*api.ClientSummaryResp, error) {
	// 获取政策信息
	var policy model.ReimbursementPolicy

	err := r.db.WithContext(c).Table("reimbursement_policy rp").
		Select("rp.id, rp.name, rp.policy_type, rp.archive").
		Where("rp.id = ?", policyID).
		Scan(&policy).Error

	if err != nil {
		return nil, err
	}
	var result = &api.ClientSummaryResp{}

	// 根据政策类型执行不同的统计查询
	if policy.PolicyType == "promotional_products" {
		// 查询推广产品申请统计
		var promotionalStats struct {
			Applied     int `json:"applied"`
			UnderUpload int `json:"under_upload"`
			UnderAudit  int `json:"under_audit"`
		}

		err = r.db.WithContext(c).Table("reimbursement_promotional_products_list").
			Select(`
                IFNULL(SUM(status=0 OR status=-3), 0) AS applied,
                IFNULL(SUM(status=1), 0) AS under_upload,
                IFNULL(SUM(status=2), 0) AS under_audit
            `).
			Where("policy_id = ? AND company_id = ? AND top_agency = ?",
				policyID, companyID, topAgency).
			Scan(&promotionalStats).Error

		if err != nil {
			return nil, err
		}

		result.Applied = promotionalStats.Applied
		result.UnderUpload = promotionalStats.UnderUpload
		result.UnderAudit = promotionalStats.UnderAudit

		// 查询推广产品核销汇总统计
		var summaryStats struct {
			NoReimburse int `json:"no_reimburse"`
		}

		err = r.db.WithContext(c).Table("reimbursement_apply_order_summary").
			Select(`
                IFNULL(SUM(status=0 OR status=-100), 0) AS no_reimburse
            `).
			Where("policy_id = ? AND company_id = ? AND top_agency = ?",
				policyID, companyID, topAgency).
			Scan(&summaryStats).Error

		if err != nil {
			return nil, err
		}

		result.NoReimburse = summaryStats.NoReimburse

	} else {
		// 查询广告费用申请统计
		var advertStats struct {
			Applied int `json:"applied"`
		}

		err = r.db.WithContext(c).Table("reimbursement_advert_expense_list").
			Select(`
                IFNULL(SUM(status=0 OR status=-1), 0) AS applied
            `).
			Where("policy_id = ? AND company_id = ? AND top_agency = ?",
				policyID, companyID, topAgency).
			Scan(&advertStats).Error

		if err != nil {
			return nil, err
		}

		result.Applied = advertStats.Applied

		// 查询广告费用核销汇总统计（包含turn_type=1的特殊逻辑）
		var summaryStats struct {
			NoReimburse int `json:"no_reimburse"`
		}

		err = r.db.WithContext(c).Table("reimbursement_apply_order_summary").
			Select(`
                IFNULL(SUM(CASE 
                    WHEN turn_type = 1 THEN status=0 OR status=-100
                    ELSE (status=0 OR status=-100) AND policy_id = ?
                END), 0) AS no_reimburse
            `, policyID).
			Where("((policy_id = ? AND turn_type = 0) OR turn_type = 1) AND company_id = ? AND top_agency = ?",
				policyID, companyID, topAgency).
			Scan(&summaryStats).Error

		if err != nil {
			return nil, err
		}

		result.NoReimburse = summaryStats.NoReimburse
	}

	// 查询核销列表统计
	var reimbursementStats struct {
		NoReturn   int `json:"no_return"`
		Reimbursed int `json:"reimbursed"`
	}

	err = r.db.WithContext(c).Table("reimbursement_list").
		Select(`
            IFNULL(SUM((status=1 OR status=2) AND confirm_status=0), 0) AS no_return,
            IFNULL(SUM(status=2), 0) AS reimbursed
        `).
		Where("policy_id = ? AND company_id = ? AND top_agency = ?",
			policyID, companyID, topAgency).
		Scan(&reimbursementStats).Error

	if err != nil {
		return nil, err
	}

	result.NoReturn = reimbursementStats.NoReturn
	result.Reimbursed = reimbursementStats.Reimbursed

	return result, nil
}

func (r *repo) GetReimbursementOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {

	offset := (req.Page - 1) * req.PageSize

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_list rl").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            rl.id,
            rl.sn,
            rl.status,
            rl.created_at,
            CASE 
                WHEN rp.type = 3 THEN rl.amount
                ELSE IF(rl.reimbursement_apply_amount = 0, rl.amount/2, rl.reimbursement_apply_amount)
            END as amount,
            rl.company,
            rl.confirm_status,
            ? as type`, 2).
		Joins("LEFT JOIN reimbursement_policy rp ON rl.policy_id = rp.id").
		Where("rl.top_agency = ? AND rl.company_id = ? AND rl.policy_id = ?",
			req.TopAgency, req.CompanyID, req.PolicyID)

	// 添加时间范围条件
	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("raos.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	// 根据状态添加不同的条件
	switch req.Status {
	case 5:
		query = query.Where("(rl.status = 1 OR rl.status = 2) AND rl.confirm_status = 0")
	case 6:
		query = query.Where("rl.status = 2")
	}

	err := query.Order("rl.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// ReimbursementClientDetail 获取核销详情
func (r *repo) ReimbursementClientDetail(c *gin.Context, orderID int, topAgency uint) (*api.ReimbursementDetail, error) {
	var detail api.ReimbursementDetail

	// 构建查询
	err := r.db.WithContext(c).
		Table("reimbursement_list rl").
		Select(`
            rl.id, rl.sn, rl.reimbursement_type, rl.reimbursement_amount,
            rl.company_id, rl.company, rl.amount, rl.status, rl.remark,
            rp.policy_type, rp.name as policy_name, rp.user_name,
            rp.phone, rp.province, rp.city, rp.district, rp.address,
            rp.explain, rl.created_at, rl.confirm_status
        `).
		Joins("RIGHT JOIN reimbursement_policy rp ON rl.policy_id = rp.id").
		Where("rl.id = ? AND rl.top_agency = ?", orderID, topAgency).
		First(&detail).Error

	if err != nil {
		return nil, err
	}

	// 获取关联数量
	var count int64
	err = r.db.WithContext(c).
		Model(&model.ReimbursementListSummaryRelation{}).
		Where("reimbursement_list_id = ?", orderID).
		Count(&count).Error

	if err != nil {
		return nil, err
	}
	detail.Count = int(count)

	return &detail, nil
}

// GetDetailSummaryInfo 获取汇总信息
func (r *repo) GetDetailSummaryInfo(ctx *gin.Context, orderID int) ([]*api.ReimbursementSummary, error) {
	var summaries []*api.ReimbursementSummary

	// 构建查询
	err := r.db.WithContext(ctx).
		Table("reimbursement_apply_order_summary rs").
		Select(`
            rs.id, rs.sn, rs.apply_order_id, rs.company,
            a.name as agency_name, rs.created_at,
            rp.name, au.phone, rs.amount, rs.actual_amount, rs.status
        `).
		Joins("LEFT JOIN reimbursement_list_summary_relation rlsr ON rs.id = rlsr.reimbursement_summary_id").
		Joins("LEFT JOIN reimbursement_policy rp ON rs.policy_id = rp.id").
		Joins("RIGHT JOIN agency a ON rs.top_agency = a.id").
		Joins("LEFT JOIN admin_users au ON rs.uid = au.id").
		Where("rlsr.reimbursement_list_id = ?", orderID).
		Find(&summaries).Error

	if err != nil {
		return nil, err
	}

	return summaries, nil
}

func (r *repo) AmountConfirm(c *gin.Context, req *api.AmountConfirmReq) error {
	// 开启事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()

	// 1. 更新核销大单状态
	err := tx.Table("reimbursement_list").
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"confirm_status":    1,
			"express_come_sn":   req.ExpressComeSn,
			"express_come_com":  req.ExpressComeCom,
			"express_come_time": now,
			"updated_at":        now,
		}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 用于记录已处理的订单ID，避免重复处理
	promotionalOrderIDs := make(map[int]bool)
	advertExpenseOrderIDs := make(map[int]bool)

	// 2. 处理每个汇总订单
	for _, summary := range req.SummaryOrder {
		updateData := map[string]interface{}{
			"express_come_sn":   req.ExpressComeSn,
			"express_come_com":  req.ExpressComeCom,
			"express_come_time": now,
			"updated_at":        now,
		}

		// 根据回寄状态设置material_return_status
		if summary.MaterialReturnStatus == 2 {
			updateData["material_return_status"] = 2
		} else {
			updateData["material_return_status"] = 1
		}

		// 根据订单类型更新不同的表
		switch summary.ApplyOrderType {
		case "promotional_products":
			if !promotionalOrderIDs[summary.ApplyOrderID] {
				err = tx.Table("reimbursement_promotional_products_list").
					Where("id = ?", summary.ApplyOrderID).
					Updates(updateData).Error
				if err != nil {
					tx.Rollback()
					return err
				}
				promotionalOrderIDs[summary.ApplyOrderID] = true
			}

		case "advert_expense":
			if !advertExpenseOrderIDs[summary.ApplyOrderID] {
				err = tx.Table("reimbursement_advert_expense_list").
					Where("id = ?", summary.ApplyOrderID).
					Updates(updateData).Error
				if err != nil {
					tx.Rollback()
					return err
				}
				advertExpenseOrderIDs[summary.ApplyOrderID] = true
			}
		}

		// 如果原始状态为0，更新汇总表的回寄状态
		if summary.MaterialReturnStatus == 0 {
			err = tx.Table("reimbursement_apply_order_summary").
				Where("id = ?", req.ID).
				Updates(map[string]interface{}{
					"material_return_status": 1,
					"updated_at":             now,
				}).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetSummaryOrderList 获取核销申请汇总单列表
func (r *repo) GetSummaryOrderList(c *gin.Context, orderID int) ([]*model.ReimbursementApplyOrderSummary, error) {
	var summaries []*model.ReimbursementApplyOrderSummary

	err := r.db.WithContext(c).
		Table("reimbursement_apply_order_summary raos").
		Select("raos.id", "raos.apply_order_id", "raos.apply_order_type", "raos.material_return_status").
		Joins("RIGHT JOIN reimbursement_list_summary_relation rlsr ON raos.id = rlsr.reimbursement_summary_id").
		Where("rlsr.reimbursement_list_id = ?", orderID).
		Scan(&summaries).Error

	if err != nil {
		return nil, err
	}

	return summaries, nil
}
