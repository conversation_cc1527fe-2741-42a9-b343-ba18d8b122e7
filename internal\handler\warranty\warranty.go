package warranty

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	warranty "marketing/internal/service"
	"mime/multipart"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

type WarrantyHandler interface {
	Create(c *gin.Context)
	List(c *gin.Context)
	Endpoints(c *gin.Context)
	GetOneWarranty(c *gin.Context)
	UpdateAssessment(c *gin.Context)
	Update(c *gin.Context)
	GroupImport(c *gin.Context)
	Export(c *gin.Context)

	Return(c *gin.Context)
	ReturnList(c *gin.Context)
	ReturnEdit(c *gin.Context)

	Exchange(c *gin.Context)
	ExchangeList(c *gin.Context)
	ExchangeGet(c *gin.Context)
	ExchangeEdit(c *gin.Context)
	// ExchangeExport(c *gin.Context)

	SNList(c *gin.Context)
}

type warrantyHandler struct {
	warrantyService warranty.InterfaceWarranty
}

func NewWarrantyHandler(svc warranty.InterfaceWarranty) WarrantyHandler {
	return &warrantyHandler{
		warrantyService: svc,
	}
}

func (w *warrantyHandler) Return(c *gin.Context) {
	var returnReq *api.ReturnReq
	if err := c.ShouldBind(&returnReq); err != nil {
		handler.Error(c, err)
		return
	}
	if err := w.warrantyService.ReturnWarranty(c, returnReq); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, "退货成功")
}

func (w *warrantyHandler) ReturnList(c *gin.Context) {
	var returnListInfo *api.ReturnListReq
	if err := c.ShouldBind(&returnListInfo); err != nil {
		handler.Error(c, err)
		return
	}

	resp, err := w.warrantyService.GetReturnList(c, returnListInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) ReturnEdit(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id")) // 退货id
	reason := c.PostForm("reason")
	if err != nil || reason == "" {
		handler.Error(c, errors.NewErr("输入参数有误"))
		return
	}
	err = w.warrantyService.ReturnEdit(c, id, reason)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (w *warrantyHandler) Exchange(c *gin.Context) {
	var req api.ExchangeReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if err := w.warrantyService.Exchange(c, &req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, "换货操作成功")
}

func (w *warrantyHandler) ExchangeList(c *gin.Context) {
	var exchangeListInfo *api.ExchangeListReq
	if err := c.ShouldBind(&exchangeListInfo); err != nil {
		handler.Error(c, err)
		return
	}

	resp, err := w.warrantyService.GetExchangeList(c, exchangeListInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) ExchangeGet(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	resp, err := w.warrantyService.ExchangeGetOneWarranty(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) Endpoints(c *gin.Context) {
	q := c.Query("q")
	endpoints := w.warrantyService.GetEndpoints(c, q)
	if len(endpoints) == 0 {
		handler.Success(c, "无搜索结果")
		return
	}
	handler.Success(c, endpoints)
}

func (w *warrantyHandler) Create(c *gin.Context) {
	var req *api.CreateWarrantyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	cnt, _ := strconv.Atoi(c.DefaultQuery("contact", "1"))
	salesmanID, _ := strconv.Atoi(c.DefaultQuery("salesman_id", "0"))
	req.Contact = cnt
	data, err := w.warrantyService.Create(c, uint(salesmanID), req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (w *warrantyHandler) List(c *gin.Context) {
	var warrantyInfo *api.WarrantyInfoReq
	if err := c.ShouldBind(&warrantyInfo); err != nil {
		handler.Error(c, err)
		return
	}
	resp, err := w.warrantyService.GetInfo(c, warrantyInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) UpdateAssessment(c *gin.Context) {
	var id int
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	action, err := strconv.Atoi(c.Param("assessment"))
	if err != nil || (action != consts.WarrantyAssessment && action != consts.WarrantyNotAssessment) {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}
	err = w.warrantyService.UpdateAssessment(c, id, uint(action))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (w *warrantyHandler) Update(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var updateReq *api.EditReq
	if err = c.ShouldBind(&updateReq); err != nil {
		handler.Error(c, err)
	}
	rowsAffected, err := w.warrantyService.Update(c, id, updateReq)
	if err != nil || rowsAffected == 0 {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (w *warrantyHandler) GroupImport(c *gin.Context) {
	// TODO: 仅Administrator和管理员可以批量导入保卡
	file, err := c.FormFile("file")
	if err != nil {
		handler.Error(c, errors.NewErr("请选择要上传的文件"))
		return
	}
	if file.Size > 2*1024*1024 {
		handler.Error(c, errors.NewErr("文件超过了最大允许上传大小 2MB"))
		return
	}
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".xls" && ext != ".xlsx" {
		handler.Error(c, errors.NewErr("只允许上传 .xls 和 .xlsx 文件"))
		return
	}
	src, err := file.Open()
	if err != nil {
		handler.Error(c, errors.NewErr("无法打开文件"))
		return
	}
	defer func(s multipart.File) {
		_ = s.Close()
	}(src)
	f, err := excelize.OpenReader(src)
	if err != nil {
		handler.Error(c, errors.NewErr(fmt.Sprintf("无法读取Excel文件: %s", err.Error())))
		return
	}
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		handler.Error(c, errors.NewErr(fmt.Sprintf("无法获取工作表数据: %s", err.Error())))
		return
	}
	if len(rows) < 2 {
		handler.Error(c, errors.NewErr("Excel文件中没有数据或只有表头"))
		return
	}
	header := rows[0]
	if len(header) < len(consts.ValidHeader) {
		handler.Success(c, errors.NewErr(fmt.Sprintf("数据列数不足，至少应有 %d 列", len(consts.ValidHeader))))
		return
	}
	for i, v := range consts.ValidHeader {
		if strings.TrimSpace(header[i]) != v {
			handler.Success(c, errors.NewErr(fmt.Sprintf("表头不匹配: 第 %c 列应为“%s”，实际为“%s”", 'A'+i, v, header[i])))
			return
		}
	}
	msg := w.warrantyService.GroupImportWarranty(c, rows, f, sheetName)
	handler.Success(c, msg)
}

func (w *warrantyHandler) Export(c *gin.Context) {
	endpointCode := c.Query("code")
	page, _ := strconv.Atoi(c.Query("page"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if endpointCode == "" {
		handler.Success(c, errors.NewErr("终端编码不能为空"))
		return
	}
	// TODO: 权限校验（目前默认为管理员）
	// user.InRoles([]string{"manager", "administrator", "warrantyExport"})
	resp, total, err := w.warrantyService.ExportWarranties(c, page, pageSize, endpointCode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
		"list":     resp,
	})
}

func (w *warrantyHandler) GetOneWarranty(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil && id != 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	resp, err := w.warrantyService.GetOneWarranty(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

func (w *warrantyHandler) ExchangeEdit(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil && id != 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	reason := c.PostForm("reason")
	if reason == "" {
		handler.Error(c, errors.NewErr("换货原因不能为空"))
		return
	}
	if err := w.warrantyService.ExchangeEdit(c, id, reason); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, "编辑成功")
}

func (w *warrantyHandler) SNList(c *gin.Context) {
	param := c.Query("sn_number_phone")
	SNMatched, _ := regexp.MatchString(`^[A-Za-z-0-9]{6,14}$`, param)
	NumberMatched, _ := regexp.MatchString(`^[A-Za-z-0-9]{8,64}$`, param)
	PhoneMatched, _ := regexp.MatchString(`^1[0-9]{10}$`, param)
	if param == "" || (!SNMatched && !NumberMatched && !PhoneMatched) {
		handler.Success(c, errors.NewErr("非法查询参数"))
		return
	}
	resp, err := w.warrantyService.SNGetWarranties(c, param)
	if err != nil {
		handler.Success(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": resp,
	})
}
